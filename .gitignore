# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
*.zip
login-error.png
admin-token-tester.html
token-tester.html
.token
phone-validation-test.js

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# temporary and migration files
/migration-temp/
/painel-abz-migration/
/painel-abz-migration-clean/
/painel-abz-migration-new/
/painel-abz-supabase-migration-clean/
/painel-abz/
/temp-files/
/debug/

# test files
/test-*.csv
/test-*.js
