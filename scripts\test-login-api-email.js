// Script para testar o login com a API usando email
require('dotenv').config();
const fetch = require('node-fetch');

async function main() {
  console.log('Testando login com a API usando email...');

  const adminEmail = '<EMAIL>';
  const adminPassword = '<PERSON>aio@2122@';

  try {
    console.log(`Tentando login com email: ${adminEmail}`);

    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: adminEmail,
        password: adminPassword,
      }),
    });

    const data = await response.json();

    console.log('Status da resposta:', response.status);
    console.log('Resposta do servidor:', data);

    if (response.ok && data.token) {
      console.log('Login realizado com sucesso!');
      console.log('Token JWT:', data.token.substring(0, 20) + '...');
      console.log('Usuário:', {
        id: data.user.id,
        email: data.user.email,
        phoneNumber: data.user.phoneNumber,
        role: data.user.role,
      });
    } else {
      console.error('Erro ao fazer login:', data.error || 'Erro desconhecido');
    }
  } catch (error) {
    console.error('Erro ao fazer login:', error);
  }
}

main()
  .catch(error => {
    console.error('Erro durante o teste:', error);
    process.exit(1);
  });
