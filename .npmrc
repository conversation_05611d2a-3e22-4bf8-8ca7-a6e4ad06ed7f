# NPM configuration file

# Use legacy peer dependencies to avoid issues with Next.js
legacy-peer-deps=true

# Increase network timeout for slow connections
network-timeout=60000

# Disable audit to speed up installation
audit=false

# Disable fund messages
fund=false

# Disable update notifier
update-notifier=false

# Set registry
registry=https://registry.npmjs.org/

# Set progress bar to false for CI environments
progress=false

# Set loglevel to error to reduce noise
loglevel=error
