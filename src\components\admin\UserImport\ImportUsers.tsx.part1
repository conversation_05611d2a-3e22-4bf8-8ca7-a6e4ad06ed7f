            {file ? (
              <div className="flex flex-col items-center">
                <FiFile className="h-10 w-10 text-green-500 mb-2" />
                <p className="text-sm font-medium text-gray-900">{file.name ? String(file.name) : ''}</p>
                <p className="text-xs text-gray-500">
                  {(() => {
                    // Garantir que o tamanho do arquivo seja tratado como número
                    try {
                      const fileSize = file.size;
                      if (typeof fileSize === 'number') {
                        return (fileSize / 1024 / 1024).toFixed(2) + ' MB';
                      } else {
                        return '0.00 MB';
                      }
                    } catch (e) {
                      return '0.00 MB';
                    }
                  })()}
                </p>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    setFile(null);
                    setPreviewData([]);
                  }}
                  className="mt-2 text-xs text-red-600 hover:text-red-800"
                >
                  {t('common.remove')}
                </button>
              </div>
            ) : isLoading ? (
