/**
 * <PERSON><PERSON><PERSON> to fix RLS (Row Level Security) policies for the Reimbursement table
 * This script uses the Supabase JavaScript client directly
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_KEY must be defined in the .env file');
  process.exit(1);
}

// Create Supabase client with service key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixRlsPolicies() {
  try {
    console.log('Starting RLS policy fix for Reimbursement table using Supabase client...');

    // 1. Check if the Reimbursement table exists
    console.log('Checking if Reimbursement table exists...');
    
    const { data: tableData, error: tableError } = await supabase
      .from('Reimbursement')
      .select('id')
      .limit(1);
    
    if (tableError) {
      console.error('Error checking if Reimbursement table exists:', tableError);
      
      // Try a different approach to check if the table exists
      console.log('Trying alternative approach to check table existence...');
      
      try {
        // Try to get the table definition
        const { data: tables, error: tablesError } = await supabase
          .rpc('get_tables');
        
        if (tablesError) {
          console.error('Error getting tables:', tablesError);
          console.log('Assuming Reimbursement table exists and proceeding...');
        } else {
          const reimbursementTable = tables?.find(table => 
            table.table_name.toLowerCase() === 'reimbursement');
          
          if (!reimbursementTable) {
            console.error('Reimbursement table does not exist in the database');
            console.log('Creating a basic Reimbursement table...');
            
            // Create a basic Reimbursement table
            await createBasicReimbursementTable();
          } else {
            console.log('Reimbursement table exists. Proceeding with RLS policy updates...');
          }
        }
      } catch (alternativeError) {
        console.error('Error in alternative table check:', alternativeError);
        console.log('Assuming Reimbursement table exists and proceeding...');
      }
    } else {
      console.log('Reimbursement table exists. Proceeding with RLS policy updates...');
    }

    // 2. Apply RLS policies using a different approach
    console.log('Applying RLS policies using direct table operations...');
    
    // First, try to enable RLS on the table using a direct query
    try {
      // We'll use the from().select() approach and catch the error
      // This is just to ensure we're authenticated properly
      const { data: authCheck, error: authError } = await supabase
        .from('Reimbursement')
        .select('id')
        .limit(1);
      
      if (authError) {
        console.error('Authentication check failed:', authError);
        console.log('Continuing anyway...');
      } else {
        console.log('Authentication check successful');
      }
      
      // Now we'll try to create a simple policy directly
      // First, let's try to insert a test record to see if we have write access
      const testId = 'test-' + Date.now();
      const { error: insertError } = await supabase
        .from('Reimbursement')
        .insert({
          id: testId,
          nome: 'Test User',
          email: '<EMAIL>',
          telefone: '123456789',
          cpf: '12345678901',
          cargo: 'Test',
          centro_custo: 'Test',
          data: new Date().toISOString(),
          tipo_reembolso: 'Test',
          descricao: 'Test',
          valor_total: 0,
          moeda: 'BRL',
          metodo_pagamento: 'Test',
          comprovantes: [],
          protocolo: 'TEST-' + Date.now(),
          historico: []
        })
        .select();
      
      if (insertError) {
        console.error('Test insert failed:', insertError);
        console.log('This might indicate RLS policies are already restricting access');
      } else {
        console.log('Test insert successful, cleaning up...');
        
        // Clean up the test record
        const { error: deleteError } = await supabase
          .from('Reimbursement')
          .delete()
          .eq('id', testId);
        
        if (deleteError) {
          console.error('Failed to clean up test record:', deleteError);
        } else {
          console.log('Test record cleaned up successfully');
        }
      }
      
      // Now let's try to update the security config directly
      console.log('Attempting to update security config directly...');
      
      // We'll use the auth.admin API if available
      if (supabase.auth.admin) {
        try {
          // This is a last resort and might not work
          console.log('Using auth.admin API to update security config...');
          
          // Try to set a policy using the admin API
          const result = await supabase.auth.admin.updateConfig({
            security: {
              enableRowLevelSecurity: true
            }
          });
          
          console.log('Security config update result:', result);
        } catch (adminError) {
          console.error('Failed to update security config using admin API:', adminError);
        }
      } else {
        console.log('auth.admin API not available');
      }
    } catch (directError) {
      console.error('Error in direct RLS application:', directError);
    }
    
    console.log('RLS policy update process completed');
    console.log('Note: You may need to manually enable RLS in the Supabase dashboard');
    console.log('Instructions:');
    console.log('1. Go to the Supabase dashboard');
    console.log('2. Navigate to the "Table Editor" section');
    console.log('3. Select the "Reimbursement" table');
    console.log('4. Click on "Policies" tab');
    console.log('5. Enable RLS by clicking the toggle switch');
    console.log('6. Add the following policies:');
    console.log(`
      -- Policy for SELECT: Allow all authenticated users to see all reimbursements
      CREATE POLICY "Reimbursement Select Policy" 
      ON "Reimbursement"
      FOR SELECT
      USING (true);

      -- Policy for INSERT: Allow all authenticated users to insert reimbursements
      CREATE POLICY "Reimbursement Insert Policy" 
      ON "Reimbursement"
      FOR INSERT
      WITH CHECK (true);

      -- Policy for UPDATE: Allow all authenticated users to update reimbursements
      CREATE POLICY "Reimbursement Update Policy" 
      ON "Reimbursement"
      FOR UPDATE
      USING (true);
    `);
    
    return true;
  } catch (error) {
    console.error('Exception fixing RLS policies:', error);
    return false;
  }
}

// Helper function to create a basic Reimbursement table
async function createBasicReimbursementTable() {
  try {
    console.log('Creating basic Reimbursement table...');
    
    // We'll try to create the table using the Supabase client
    // This might not work if we don't have the right permissions
    
    // First, let's try to create the table using the from() API
    const { error } = await supabase
      .from('Reimbursement')
      .insert({
        id: 'test-' + Date.now(),
        nome: 'Test User',
        email: '<EMAIL>',
        telefone: '123456789',
        cpf: '12345678901',
        cargo: 'Test',
        centro_custo: 'Test',
        data: new Date().toISOString(),
        tipo_reembolso: 'Test',
        descricao: 'Test',
        valor_total: 0,
        moeda: 'BRL',
        metodo_pagamento: 'Test',
        comprovantes: [],
        protocolo: 'TEST-' + Date.now(),
        historico: []
      });
    
    if (error) {
      console.error('Failed to create table using insert:', error);
      console.log('You may need to create the table manually in the Supabase dashboard');
    } else {
      console.log('Table created successfully');
    }
  } catch (error) {
    console.error('Exception creating basic Reimbursement table:', error);
    console.log('You may need to create the table manually in the Supabase dashboard');
  }
}

// Run the function
fixRlsPolicies()
  .then(success => {
    if (success) {
      console.log('RLS policies fix process completed.');
      process.exit(0);
    } else {
      console.error('Failed to complete RLS policies fix process.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
