import enUS from './locales/en-US';
import ptBR from './locales/pt-BR';
import { getCacheValue, setCacheValue } from '@/lib/cache';

export type Locale = 'en-US' | 'pt-BR';

export const locales: Record<Locale, any> = {
  'en-US': enUS,
  'pt-BR': ptBR,
};

export const defaultLocale: Locale = 'pt-BR';

export function getTranslation(locale: Locale, key: string, defaultValue?: string): string {
  // Check cache first
  const cacheKey = `i18n:${locale}:${key}`;
  const cachedValue = getCacheValue<string>(cacheKey);

  if (cachedValue !== undefined) {
    return cachedValue;
  }

  // Special handling for card IDs with hyphens
  // If the key starts with 'cards.' and contains hyphens, try to find a version without hyphens
  let modifiedKey = key;
  if (key.startsWith('cards.') && key.includes('-')) {
    // Create a version of the key without hyphens
    modifiedKey = key.replace(/-/g, '');
  }

  // Try with the original key first
  const keys = key.split('.');
  let translation: any = locales[locale];
  let found = true;

  // Try to find the translation in the current locale
  for (const k of keys) {
    if (!translation || !translation[k]) {
      found = false;
      break;
    }
    translation = translation[k];
  }

  // If found in current locale, cache and return it
  if (found) {
    setCacheValue(cacheKey, translation);
    return translation;
  }

  // If not found and we have a modified key (without hyphens), try that
  if (!found && modifiedKey !== key) {
    const modifiedKeys = modifiedKey.split('.');
    translation = locales[locale];
    found = true;

    for (const k of modifiedKeys) {
      if (!translation || !translation[k]) {
        found = false;
        break;
      }
      translation = translation[k];
    }

    if (found) {
      setCacheValue(cacheKey, translation);
      return translation;
    }
  }

  // Try to find in default locale if not found in current locale
  if (locale !== defaultLocale) {
    // Try with original key
    translation = locales[defaultLocale];
    found = true;

    for (const k of keys) {
      if (!translation || !translation[k]) {
        found = false;
        break;
      }
      translation = translation[k];
    }

    if (found) {
      setCacheValue(cacheKey, translation);
      return translation;
    }

    // Try with modified key (without hyphens) in default locale
    if (modifiedKey !== key) {
      const modifiedKeys = modifiedKey.split('.');
      translation = locales[defaultLocale];
      found = true;

      for (const k of modifiedKeys) {
        if (!translation || !translation[k]) {
          found = false;
          break;
        }
        translation = translation[k];
      }

      if (found) {
        setCacheValue(cacheKey, translation);
        return translation;
      }
    }
  }

  // Cache and return default value or key if not found in any locale
  const result = defaultValue || key;
  setCacheValue(cacheKey, result);
  return result;
}

export function getBrowserLocale(): Locale {
  if (typeof window === 'undefined') {
    return defaultLocale;
  }

  try {
    // Tentar obter o idioma do navegador
    const browserLocale = navigator.language ||
                         (navigator as any).userLanguage ||
                         (navigator as any).browserLanguage ||
                         (navigator as any).systemLanguage ||
                         defaultLocale;

    console.log('Idioma detectado do navegador:', browserLocale);

    // Verificar se o idioma começa com 'pt' (português)
    if (browserLocale.toLowerCase().startsWith('pt')) {
      return 'pt-BR';
    }

    // Caso contrário, usar inglês
    return 'en-US';
  } catch (error) {
    console.error('Erro ao detectar idioma do navegador:', error);
    return defaultLocale;
  }
}

export function getLocalStorageLocale(): Locale | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const storedLocale = localStorage.getItem('locale') as Locale;

  if (storedLocale && Object.keys(locales).includes(storedLocale)) {
    return storedLocale;
  }

  return null;
}

export function setLocalStorageLocale(locale: Locale): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem('locale', locale);
}

export function getInitialLocale(): Locale {
  // First check localStorage
  const localStorageLocale = getLocalStorageLocale();
  if (localStorageLocale) {
    return localStorageLocale;
  }

  // Then check browser locale
  const browserLocale = getBrowserLocale();
  if (browserLocale) {
    return browserLocale;
  }

  // Fallback to default locale
  return defaultLocale;
}
