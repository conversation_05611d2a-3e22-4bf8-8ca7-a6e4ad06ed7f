@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-color: #005dff;
  --secondary-color: #6339F5;
}

body {
  background: var(--background);
  color: var(--foreground);
}

/* Estilos globais para imagens */
img {
  width: auto;
  height: auto;
  max-width: 100%;
  object-fit: contain;
}

/* Componentes personalizados */
@layer components {
  .abz-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .abz-card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .abz-button-primary {
    @apply bg-abz-blue text-white rounded-md px-4 py-2 font-medium hover:bg-abz-blue-dark transition-colors;
  }

  .abz-button-secondary {
    @apply bg-gray-200 text-gray-800 rounded-md px-4 py-2 font-medium hover:bg-gray-300 transition-colors;
  }

  .abz-button-danger {
    @apply bg-red-600 text-white rounded-md px-4 py-2 font-medium hover:bg-red-700 transition-colors;
  }
  
  /* Ensure text is visible on ABZ blue background buttons */
  .bg-abz-blue {
    @apply text-white;
  }
  
  /* Fix for login button text visibility */
  button.bg-abz-blue {
    color: white !important;
  }
}

/* Definições básicas da fonte e cores */
:root {
  --font-plus-jakarta: 'Plus Jakarta Sans', system-ui, sans-serif;

  /* Estas variáveis serão atualizadas dinamicamente pelo SiteConfigContext */
  --primary-color: #005dff;
  --secondary-color: #6339F5;
}

/* Estilos para garantir que a fonte seja aplicada */
html {
  font-family: var(--font-plus-jakarta);
}

body {
  font-family: var(--font-plus-jakarta);
  @apply bg-gray-50;
}

/* Útils para garantir que as classes do Tailwind comuns sejam sempre geradas */
.abz-gradient {
  @apply bg-gradient-to-br from-blue-100 to-purple-100;
}

.abz-header {
  @apply bg-white shadow-md;
}

/* Aplicação dinâmica das cores do tema */
.text-primary {
  color: var(--primary-color);
}

.bg-primary {
  background-color: var(--primary-color);
  color: white; /* Ensure text is white on primary background */
}

.border-primary {
  border-color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.bg-secondary {
  background-color: var(--secondary-color);
  color: white; /* Ensure text is white on secondary background */
}

.border-secondary {
  border-color: var(--secondary-color);
}

/* Fix for button text color */
.bg-abz-blue, .hover\:bg-abz-blue:hover {
  color: white !important;
}

.bg-abz-blue-dark, .hover\:bg-abz-blue-dark:hover {
  color: white !important;
}

/* Estilos para ícones do Material Design */
.material-icon-replacement {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  width: 24px;
  height: 24px;
  font-size: 24px;
  line-height: 1;
}

.material-icon-replacement .material-icons {
  font-size: inherit;
  line-height: inherit;
}

/* Estilos para tags de ícones personalizadas - esconder para evitar renderização incorreta */
book, description, policy, calendar_today, newspaper,
receipt, payments, schedule, assessment, admin_panel_settings,
dashboard, people, person, settings, menu, close, logout,
layers, list, edit, image, check, alert, info, warning,
error, success {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  position: absolute !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
